syntax = "proto3";
package bfdx_proto;

//数据库的所有时间为UTC时间,客户端显示时请根据用户的时间进行相应的转换
//新增加的表必须在最后，否则会导致前后端rpc命令冲突

//系统设置表
message db_sys_config {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 2;

  //@table varchar(64) unique not null
  //配置名
  string conf_key = 3;

  //@table text
  //配置的值
  string conf_value = 4;
}

//表各种操作时间
//客户端有时需要查询后台是否已经更新了数据,可以通过此表来得到初步的信息
message db_table_operate_time {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 2;

  //@table varchar(64) unique
  //数据库表名
  string table_name = 3;

  //@table timestamp
  //最后修改时间
  string last_modify_time = 4;

  //@table int
  //最后修改的操作 1:insert 2:update 3:delete
  int32 last_modify_operation = 5;

  //@table int
  //最后修改时影响的行数
  int32 last_modify_rows = 6;
}

//组织架构表
message db_org {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 2;

  //@table varchar(64) unique not null
  //组织机构编号
  string org_self_id = 3;

  //@table int default 100
  //排序值,由用户指定,不用管拼音或者其它语种的排序
  int32 org_sort_value = 4;

  //@table varchar(32) unique not null
  //机构名称,缩写
  string org_short_name = 5;

  //@table varchar(256)
  //机构名称,全称
  string org_full_name = 6;

  //@table text
  //机构详细描述
  string note = 7;

  //@table int default 2
  //2:真实机构,1:虚拟机构 虚拟机构没有真实设备和用户,只是引用真实机构的数据,相当于额外的分组 100:临时组 101：任务组 102:自动失效的临时组
  int32 org_is_virtual = 8;

  //@table varchar(8) unique
  //DMR ID,可用作组呼的ID
  string dmr_id = 9;

  //@table uuid default '11111111-1111-1111-1111-111111111111'
  //组织机构的图标
  string org_img = 10;

  //@table uuid not null default '11111111-1111-1111-1111-111111111111'
  //此组织的上级机构device
  string parent_org_id = 11;

  //@table jsonb not null default '{}'::jsonb
  //如果是prochat网关产生的动态组,则有prochatID:{"prochatID":12345}
  //org的配置信息
  string setting = 12;

  //@table int default 0
  //动态组状态  1:正常 10:失效 / 删除中
  int32 dynamic_group_state = 13;

  //@table uuid
  //创建者优先级
  string creator = 14;
}

//用户的一些图片数据,地图点icon等
message db_image {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid REFERENCES db_org(rid) ON DELETE CASCADE
  //所属的群组
  string org_id = 3;

  //@table text
  //文件名
  string file_name = 4;

  //@table text
  //文件内容,经过base64编码,就是html img的dataurl
  string file_content = 5;

  //@table text
  //文件内容的base64(sha256(file_content)
  string hash = 6;
}

//基站列表
message db_base_station {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid REFERENCES db_org(rid) ON DELETE CASCADE default '00000000-0000-0000-0000-000000000000'
  //所属的群组
  string org_id = 2;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 3;

  //@table varchar(16) not null
  //基站编号
  string self_id = 4;

  //@table varchar(16) not null
  string base_station_name = 5;

  //@table double precision
  //经度
  double lon = 6;

  //@table double precision
  //纬度
  double lat = 7;
}

//控制器设备表
message db_controller {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 2;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE default '00000000-0000-0000-0000-000000000000'
  //设备所属的群组
  string org_id = 3;

  //@table text default ''
  //所属的基站
  string base_station_rid = 4;

  //@table varchar(16) not null
  //控制器编号
  string self_id = 5;

  //@table text not null unique
  //控制器DMR-ID
  string dmr_id = 6;

  //@table double precision
  //经度
  double lon = 7;

  //@table double precision
  //纬度
  double lat = 8;

  //@table text
  //控制器的详细描述
  string note = 9;

  //@table jsonb not null default '{}'::jsonb
  //控制器设备的配置信息
  string setting = 10;

  //@table jsonb not null default '{}'::jsonb
  //控制器信号覆盖范围,point_array,polyline
  string signal_aera = 11;

  //@table jsonb not null  default '{}'::jsonb
  //{'valid':1,'phone_number':'123456789j0','fee_start':'2016-10-01','fee_end':'2017-01-01','alert_ahead_days':7}
  //控制器sim卡信息
  string sim_info = 12;

  //@table text not null default '0'
  //默认的语音会议室
  string room = 13;

  //@table text  unique
  //预分配的voip电话号码
  string sip_no = 14;

  //@table int
  //最大可用的联网时隙
  int32 net_time_slot = 15;

  //@table int default 0
  //控制器类型 0:中继 1:同播中继 2:电话网关(tg810网关) 3:同播控制器 4: 虚拟集群控制器 5: 虚拟集群中继 10:sip网关 12:mesh网关 14:prochat网关 16:sip server网关
  //prochat 网关rid固定为'14141414-1414-1414-1414-141414141414'，包括相应的终端和用户rid也一样
  //sip server网关rid固定为'16161616-1616-1616-1616-161616161616'
  int32 controller_type = 16;

  //@table text
  //安装位置/地址
  string location = 17;

  //@table boolean default false
  //中继是否带插话功能,带的话需要相同dmrid的终端存在
  bool can_talk = 18;

  //@table uuid default '00000000-0000-0000-0000-000000000000'
  // 同播中继上级控制器rid
  string simulcast_parent = 19;

  //@table int not null default 0
  //常规dmr手台是否允许联网呼叫
  int32 traditional_dmr_allow_net_call = 20;
}

//控制器状态
message db_controller_last_info {
  //@table uuid primary key
  //控制器ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE default '00000000-0000-0000-0000-000000000000'
  //设备所属的群组
  string org_id = 3;

  //@table timestamp not null default '2000-01-01 00:00:00'
  //最后上来数据的时间
  string last_data_time = 4;

  //@table int default 0
  //当前连接状态 0:offline 1:online
  int32 connected = 5;
}

//控制器上线历史表,按月分表
message db_controller_online_history {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table varchar(8)
  //dmr-id
  string controller_dmr_id = 2;

  //@table timestamp
  //satation action time
  string action_time = 3;

  //@table int
  //1: register 2:disconnect refer to action_code of cc01
  int32 action_code = 4;

  //@table text
  //action description
  string note = 5;
}

//电话网关黑白名单
message db_phone_gateway_filter {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //所属的群组
  string org_id = 2;

  //@table text  unique not null
  //名称
  string name = 3;

  //@table timestamp
  //最后修改时间(保存时间)
  string last_modify_time = 4;

  //@table jsonb default '[]'::jsonb
  //拨入黑名单
  string in_black = 5;

  //@table boolean default false
  //拨入黑名单是否启用
  bool in_black_enable = 6;

  //@table jsonb default '[]'::jsonb
  //拨入白名单
  string in_white = 7;

  //@table boolean default false
  //拨入白名单是否启用
  bool in_white_enable = 8;

  //@table jsonb default '[]'::jsonb
  //拨出黑名单
  string out_black = 9;

  //@table boolean default false
  //拨出黑名单是否启用
  bool out_black_enable = 10;

  //@table jsonb default '[]'::jsonb
  //拨出白名单
  string out_white = 11;

  //@table boolean default false
  //拨出白名单是否启用
  bool out_white_enable = 12;

  //@table text
  string note = 13;

  //@table jsonb default '{}'::jsonb
  //json设置
  string setting = 14;
}

//对讲机设备表
message db_device {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 2;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE default '00000000-0000-0000-0000-000000000000'
  //设备所属的群组
  string org_id = 3;

  //@table varchar(16) not null unique
  //设备名称
  string self_id = 4;

  //@table varchar(16) not null unique
  //设备DMR-ID
  string dmr_id = 5;

  //@table text default ''
  //对讲机所属的虚拟群组,逗号分隔的群组rid
  string vir_orgs = 6;

  //@table uuid default '00000000-0000-0000-0000-000000000000'
  //对讲机所属用户,db_user中的rid
  string device_user = 7;

  //@table text default ''
  //设备备注信息
  string note = 8;

  //@table int not null default 0
  // 设备类型 0:对讲机手台 1：车台 2:指挥座席 3:电话网关设备(tg810) 4:中继虚拟终端
  // 5:互联网关终端 6:模拟网关终端 7:数字网关终端 8:2.4G物联巡查终端
  // 9:传统常规dmr手台 10:sip网关终端 11:虚拟集群对讲手台 12:mesh网关终端
  // 13:mesh终端
  // 14:prochat公网终端 setting中有相应的prochatID:{"prochatID":12345}
  // 15:prochat网关终端，不能手动删除，只能通过prochat网关设备一起删除
  // 16:sip 电话终端,直接sip接入8100系统，配置1信道为默认收听组，支持动态配置收听组
  // 21:基地台  22:android模拟终端 23:公网poc终端
  int32 device_type = 9;

  //@table timestamp not null default now_utc()
  //频道数据最后修改时间
  string channel_last_modify_time = 10;

  //@table jsonb not null default '{"channels": []}'::jsonb
  //频道配置数据
  // type DeviceChannel []struct {
  //     No          int      `json:"No"`
  //     SendGroup   string   `json:"sendGroup"`
  //     ListenGroup []string `json:"listenGroup"`
  // }
  string channel = 11;

  //@table int
  //优先级
  int32 priority = 12;

  //@table uuid REFERENCES db_phone_gateway_filter(rid) ON DELETE set null
  //关联的电话黑白名单
  string gateway_filter_rid = 13;

  //@table jsonb not null default '{}'::jsonb
  //设备的配置信息
  string setting = 14;

  //@table timestamp not null default now_utc()
  //写频配置最后更新时间
  string last_rf_config_time = 15;

  //@table timestamp not null default now_utc()
  //写频配置最后写入对讲机时间
  string last_rf_write_time = 16;

  //@table int not null default 0
  //常规dmr手台是否允许呼叫
  int32 traditional_dmr_allow_net_call = 17;

  //@table text 
  //集群机归属组dmrid
  string dev_group = 18;

  //@table jsonb not null default '{}'::jsonb
  //公网终端的配置信息
  string poc_setting = 19;

  //@table timestamp not null default now_utc()
  //poc_setting_last_modify_time
  string poc_setting_last_modify_time = 20;

  //@table int not null default 0
  //是否可以呼叫不在线的目标
  // 0:not allow 1:allow
  int32 allow_call_offline = 21;
}
//对讲机最后的数据信息
message db_device_last_info {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 2;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE default '00000000-0000-0000-0000-000000000000'
  //设备所属的群组
  string org_id = 3;

  //@table varchar(16) not null unique
  //设备DMR-ID
  string dmr_id = 5;

  //@table timestamp  default '2000-01-01 00:00:00'
  //最后数据时间
  string last_data_time = 6;

  //@table uuid default '00000000-0000-0000-0000-000000000000'
  //最后打卡人员的rid
  string last_rfid_person = 7;

  //@table timestamp  default '2000-01-01 00:00:00'
  //最后读取用户身份卡时的时间
  string last_rfid_person_time = 4;

  //@table varchar(16) default ''
  //最后读取的rfid卡号
  string last_rfid = 8;

  //@table timestamp default '2000-01-01 00:00:00'
  //最后读取rfid的时间
  string last_rfid_time = 9;

  //@table timestamp default '2000-01-01 00:00:00'
  //最后定位的时间
  string last_gps_time = 10;

  //@table double precision default 1000
  //最后定位的经度
  double last_lon = 11;

  //@table double precision default 1000
  //最后定位的纬度
  double last_lat = 12;

  //@table int default 0
  //设备状态 0=开机；1=禁听锁机,2=禁发锁机,3=禁发禁听锁机
  int32 device_lock_state = 13;

  //@table text default ''
  //对讲机最后的状态信息
  string ms_status = 14;

  //@table text default ''
  //最后接收的控制器id
  string last_controller = 15;

  //@table timestamp not null default '2000-01-01 00:00:00'
  //设备的最后开机时间
  string last_poweron_time = 16;

  //@table timestamp not null default '2000-01-01 00:00:00'
  //设备的最后开机时间
  string last_poweroff_time = 17;

  //@table timestamp not null default '2000-01-01 00:00:00'
  //设备的最后不定位时间，上传GPS数据为V
  string last_gps_invalid_time = 18;

  //@table jsonb not null default '{}'::jsonb
  //额外的状态信息
  string opt_status = 19;

  //@table int not null default 0
  //最后gps开关状态
  //0:未知 11:开 10:关 14:N/A
  int32 last_gps_switch_state = 20;
}

//用户职称表
message db_user_title {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 2;

  //@table varchar(64) not null unique default ''
  //职称名
  string title_name = 3;

  //@table text default ''
  //职称备注
  string note = 4;

  //@table int
  //职称排序值
  int32 title_sort_value = 5;
}

//用户数据表
message db_user {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 2;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //用户所属的群组
  string org_id = 3;

  //@table varchar(16) not null unique
  //用户自编号
  string self_id = 4;

  //@table varchar(32) not null default ''
  //用户名
  string user_name = 5;

  //@table text default ''
  //用户职称,可能有个,职称rid的列表,逗号分隔
  string user_title = 6;

  //@table varchar(64) default ''
  //用户电话
  string user_phone = 7;

  //@table uuid REFERENCES db_image(rid) ON DELETE set DEFAULT default '*************-2222-2222-************'
  //用户图片
  string user_image = 8;

  //@table varchar(16) default ''
  //用户的身份卡ID
  string user_rfid = 9;

  //@table varchar(32) unique
  //用户登录名
  string user_login_name = 10;

  //@table text default ''
  //用户登录密码,为base64(sha256(user_name+user_password)),系统不保存真实的密码
  string user_login_pass = 11;

  //@table jsonb not null default '{}'::jsonb
  //用户的一些个人配置
  string user_setting = 12;

  //@table text default ''
  //note
  string note = 13;

  //@table boolean default true
  // @Deprecated 允许登录调度管理网页，像android app就不允许登录网页，但是可以只登录app
  bool allow_login_manage = 14;

  //@table uuid
  //creator rid
  string creator = 15;

  //@table boolean default false
  //允许登录管理网页
  bool is_admin = 16;
  //@table boolean default false
  // 允许登录调度网页
  bool is_dispatcher = 17;
}

//用户群组权限表
message db_user_privelege {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 2;

  //@table uuid not null REFERENCES db_user(rid) ON DELETE CASCADE
  //用户rid
  string user_rid = 3;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //有权限的群组
  string user_org = 4;

  //@table int default 0
  //是否包含下级群组
  int32 include_children = 5;
}

//用户登录的session id表
message db_user_session_id {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 2;

  //@table uuid not null
  //用户id
  string user_rid = 3;

  //@table int not null default 1
  //登录渠道 1:web 2:phone 3:pc  4:android
  int32 login_way = 4;

  //@table uuid not null
  //session id
  string session_id = 5;

  //@table timestamp
  //有效期
  string effective_time = 6;
}



//虚拟群组信息表
message db_virtual_org {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 2;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //虚拟群组rid
  string org_id = 3;

  //@table uuid not null REFERENCES db_user(rid) ON DELETE CASCADE
  //群组成员rid
  string virtual_user = 4;
}

//用户自己的一些地图标志
message db_map_point {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 2;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //所属的群组
  string org_id = 3;

  //@table varchar(16) not null unique
  //编号
  string self_id = 4;

  //@table varchar(16) not null
  //标志名称
  string point_name = 5;

  //@table varchar(16) not null
  //地图上显示的名称
  string map_display_name = 6;

  //@table text
  //备注
  string note = 7;
  //@table double precision
  //longitude
  double lon = 8;
  //@table double precision
  //latitude
  double lat = 9;
  //@table int
  //开始显示的级别
  int32 start_show_level = 10;
  //@table smallint
  //颜色R
  int32 color_r = 11;
  //@table smallint
  //颜色G
  int32 color_g = 12;
  //@table smallint
  //颜色B
  int32 color_b = 13;
  //@table uuid REFERENCES db_image(rid) ON DELETE RESTRICT
  //点的图标信息
  string point_img = 14;
  //@table int default 0
  //点是颜色点=0,还是图标点=1
  int32 img_or_color_point = 15;

  //@table int not null default 16
  //地图显示的大小,限制最大128
  int32 marker_width = 16;
  //@table int not null default 16
  //地图显示的大小,限制最大128
  int32 marker_height = 17;

  //@table int default 0
  //标记点类型,0:普通标记点 1:单位标记点
  int32 marker_type = 18;
}

//巡查线路点
message db_line_point {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 2;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //所属的群组
  string org_id = 3;

  //@table varchar(16) not null
  //编号
  string point_id = 4;

  //@table varchar(16) not null
  //标志名称
  string point_name = 5;

  //@table varchar(16) not null
  //地图上显示的名称
  string map_display_name = 6;

  //@table text
  //备注
  string note = 7;
  //@table double precision
  //longitude
  double lon = 8;
  //@table double precision
  //latitude
  double lat = 9;
  //@table int
  //开始显示的级别
  int32 start_show_level = 10;
  //@table smallint
  //颜色R
  int32 color_r = 11;
  //@table smallint
  //颜色G
  int32 color_g = 12;
  //@table smallint
  //颜色B
  int32 color_b = 13;
  //@table uuid REFERENCES db_image(rid) ON DELETE RESTRICT
  //点的图标信息
  string point_img = 14;
  //@table int default 0
  //点是颜色点,还是图标点
  int32 img_or_color_point = 15;

  //@table int default 0
  //巡查点类型 1无源点，2有源点,3gps虚拟点,4基站巡查点
  int32 point_type = 16;

  //@table varchar(16) unique
  //巡查点的rfid,虚拟点时也要写入一个唯一值
  string point_rfid = 17;

  //@table int default 50
  //gps虚拟点的半径,单位M
  int32 gps_point_radius = 18;

  //@table timestamp
  //有源点最后低压报警时间,null为没有报警过
  string last_lpalarm_time = 19;

  //@table boolean default false
  //当前点是不是在低压报警状态
  bool last_lpalarm_state = 20;
}

//巡查点最新信息
message db_line_point_latest_info {
  //@table uuid primary key
  //巡查点ID
  string rid = 1;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 2;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //所属的群组
  string org_id = 3;

  //@table timestamp
  //最后巡查时间
  string last_check_time = 8;
  //@table uuid
  //最后检查的设备rid
  string last_check_device_id = 9;

  //@table uuid
  //最后检查的用户rid
  string last_check_user_id = 11;
}

//巡查线路主表
message db_line_master {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 2;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //所属的群组
  string org_id = 3;

  //@table varchar(16) not null
  //编号
  string line_id = 4;

  //@table varchar(16) not null
  //名称
  string line_name = 5;
  //@table text
  //note
  string note = 6;
  //@table int default 0
  //线路包含的点数量
  int32 point_count = 7;

  //@table int not null default 0
  //此线路已经删除过点时为1,需要提示用户再确认线路是否完整,如果修改完成要设置为0
  int32 line_detail_modify = 8;
}

//巡查线路细表
message db_line_detail {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 2;

  //@table uuid REFERENCES db_line_master(rid) ON DELETE CASCADE
  //线路的rid
  string line_id = 3;
  //@table uuid REFERENCES db_line_point(rid) ON DELETE CASCADE
  //点的rid
  string point_id = 4;

  //@table int
  //点在线路中的序号
  int32 point_no = 5;
  //@table int not null default 10
  //最快到达时间,单位为分钟
  int32 ahead_time = 6;

  //@table int not null default 10
  //最迟到达时间,单位为分钟
  int32 delay_time = 7;
}

//巡查规则表
message db_rfid_rule_master {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 2;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //所属的群组
  string org_id = 3;

  //@table varchar(16) not null
  //编号
  string rule_id = 4;

  //@table varchar(16) not null
  //标志名称
  string rule_name = 5;

  //@table text
  //note
  string note = 6;

  //@table boolean default false
  //星期一
  bool day_1 = 7;
  //@table boolean default false
  //星期二
  bool day_2 = 8;
  //@table boolean default false
  //星期三
  bool day_3 = 9;
  //@table boolean default false
  //星期四
  bool day_4 = 10;
  //@table boolean default false
  //星期五
  bool day_5 = 11;
  //@table boolean default false
  //星期六
  bool day_6 = 12;
  //@table boolean default false
  //星期七
  bool day_7 = 13;

  //@table time
  //巡查开始的时间
  string check_start_time = 14;
  //@table int
  //巡查一次需要的时间,单位为分钟
  int32 check_all_time = 15;
  //@table int not null default 1
  //总共需要巡查的次数
  int32 check_count = 16;

  //@table int default 0
  //线路巡查规则生效时间的类型 0:总是生效 1:按有效时间段
  int32 rule_effective_type = 17;

  //@table timestamp
  //规则开始生效时间
  string rule_effective_start = 18;
  //@table timestamp
  //规则生效结束时间
  string rule_effective_end = 19;

  //@table uuid  REFERENCES db_line_master(rid) ON DELETE SET NULL
  //巡查规则对应的线路
  string rule_line_rid = 20;
}

//开关机数据表,按月分表
message db_device_power_onoff {
  //@table uuid primary key
  //行ID
  string rid = 1;
  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //对讲机所属的群组
  string org_id = 3;

  //@table uuid REFERENCES db_device(rid) ON DELETE CASCADE
  //device rid
  string device_id = 4;

  //@table uuid
  //对讲机使用人员id
  string user_id = 6;

  //@table timestamp not null
  //动作时间
  string action_time = 8;

  //@table int
  //动作类型 0=关机；1=正常开机；2=电池开机；3=欠压复位开机
  int32 action_type = 9;
}

//上班下班打卡数据表,按月分表
message db_user_check_in_history {
  //@table uuid primary key
  //行ID
  string rid = 1;
  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //对讲机所属的群组
  string org_id = 3;

  //@table uuid REFERENCES db_device(rid) ON DELETE CASCADE
  //device rid
  string device_id = 4;

  //@table uuid
  //对讲机使用人员id
  string user_id = 6;

  //@table timestamp not null
  //读卡时间
  string action_time = 8;

  //@table int
  //动作类型 1上班,2上班中打卡,3下班,
  int32 action_type = 9;

  //@table varchar(10)
  //身份卡的id
  string rfid_id = 10;
}

//rfid巡查历史表,按月分表
message db_rfid_history {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //对讲机所属的群组
  string org_id = 2;

  //@table timestamp
  //巡查时间
  string check_time = 3;
  //@table uuid
  //巡查人员rid
  string checker_id = 4;
  //@table int not null default 0
  //打卡设备类型 0:对讲终端 1:对讲机 2:工牌 3:物卡 4：烟感  5：设备开关盒  6：信标开关盒
  int32 dev_type = 5;
  //@table uuid
  //对讲机rid
  string device_id = 6;

  //@table timestamp
  //接收时间,不一定是实时的
  string receive_time = 8;

  //@table varchar(16)
  //接收的控制器名称
  string receiver = 9;

  //@table uuid REFERENCES db_line_point(rid) ON DELETE CASCADE
  //点的标识
  string point_id = 10;
}

//gps位置历史表,按月分表
message db_gps_history {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //对讲机所属的群组
  string org_id = 2;

  //@table timestamp
  //gps time
  string gps_time = 3;
  //@table boolean
  //gps valid
  bool gps_fixed = 4;
  //@table double precision
  //longitude
  double lon = 5;
  //@table double precision
  //latitude
  double lat = 6;
  //@table double precision
  //gps speed
  double speed = 7;
  //@table int
  //gps direction, north=0
  int32 direction = 8;

  //@table int
  //gps altitude
  int32 altitude = 9;

  //@table uuid
  //device rid
  string device_id = 10;
  //@table varchar(16)
  //device name
  //string device_name=10;

  //@table uuid
  //相关用户rid
  string person_id = 11;
  //@table varchar(32)
  //相关用户名

  //@table text
  //对讲机状态
  string device_status = 12;

  //@table varchar(12)
  //是什么命令上来的gps数据
  string up_cmd = 13;
}

//报警历史,要分表了,因为报警可能会非常多,客户端需要编辑此表,分表客户端处理需要特殊处理
message db_alarm_history {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //对讲机所属的群组
  string org_id = 2;

  //@table timestamp
  //报警时间
  string alarm_time = 3;
  //@table uuid
  //对讲机rid
  string device_id = 4;

  //@table int not null default 0
  //报警设备类型 0:对讲终端 1:对讲机 2:工牌 3:物卡 4：烟感  5：节能灯  6：温湿度
  int32 alarm_dev_type = 5;

  //@table uuid
  //对讲机使用人员id
  string person_id = 6;

  //@table text
  //处理人员rid
  string dealler_rid = 8;
  //@table timestamp
  //处理时间
  string dealler_time = 9;
  //@table jsonb  not null default '{}'::jsonb
  //处理内容
  string dealler_result = 10;

  //@table text
  //报警列表,数值列表，逗号分隔, 1=紧急报警；2=强行脱网报警；3=发生欠压报警；4=GPS故障报警;5=GPS遮挡;6:防拆报警;7:节能灯人感应报警;8:节能灯声音感应报警
  //bcxx类报警 41=入界报警;42=出界报警;51=入界回岗;52=出界离岗;61=移动监控走动提示;62=移动监控停留报警
  string alarm_type = 11;
}

//对讲机通话历史,按月分表
message db_sound_history {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //对讲机所属的群组
  string org_id = 2;

  //@table uuid
  //对讲机rid
  string device_id = 3;

  //@table text
  //发起者额外信息,类似电话网关打电话时的电话号码
  string source_info = 4;

  //@table timestamp
  //通话开始时间
  string sound_time = 5;

  //@table int default 0
  //对讲时长,秒
  int32 sound_len = 6;
  //@table int
  //对讲所在信道
  int32 channel = 7;

  //@table varchar(16)
  //对讲所在控制器dmr-id
  string controller = 8;

  //@table text
  //声音文件名
  string file_name = 9;

  //@table uuid
  //对讲机使用人员id
  string person_id = 10;

  //@table text not null default ''
  //通话目标dmrid
  string target = 11;
  //@table text
  //通话目标额外信息,可能是电话号码
  string target_info = 12;
}

//还没发送的命令
message db_not_send_cmd {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //用户/对讲机所属的群组
  string org_id = 2;

  //@table timestamp
  //预定发送时间
  string schedule_time = 6;

  //@table timestamp
  //截止日期,(有效期)
  string stop_time = 7;
}

//已经发送的命令列表
message db_sent_cmd_history {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //(发起者)用户/对讲机所属的群组
  string org_id = 2;

  //@table timestamp
  //命令接收的时间
  string input_time = 3;

  //@table uuid
  //输入的用户id,可能是对讲机设备,不一定就是系统软件的用户
  string input_user_id = 4;

  //@table int not null default 0
  //0:系统用户发起的命令,1:对讲机发起的命令
  int32 sender_type = 5;

  //@table timestamp
  //预定发送时间
  string schedule_time = 6;

  //@table timestamp
  //截止日期,(有效期)
  string stop_time = 7;

  //@table jsonb not null default '{"group":[],"device":[],"response":{}}'::jsonb
  //发送目标列表,dmr_id数组
  //回应状况为dmr_id:{res_time:xxxx,res_con:dmr_id}
  string cmd_target = 8;

  //@table jsonb not null default '{}'::jsonb
  //每一个发送目标对应要发送控制器列表和序号
  string cmd_target_con_seq = 9;

  //@table text
  //发送的命令,如CB01/CB24
  string send_cmd = 10;

  //@table jsonb not null default '{}'::jsonb
  //cbxx json
  string orig_cbxx = 11;

  //@table jsonb not null default '{}'::jsonb
  //一些命令相关的参数
  string cmd_params = 12;

  //@table jsonb not null default '{}'::jsonb
  //发送时间列表
  string send_time_list = 13;
}

//对讲机注册信息
message db_device_register_info {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table timestamp
  //命令接收的时间
  string receive_time = 3;

  //@table text
  //接收控制器id
  string con_ch = 4;

  //@table text
  //设备id
  string dmr_id = 5;

  //@table text
  //经销商id
  string seller_id = 6;

  //@table text
  //注册码
  string sn = 7;
}

//通话调度/切换信道历史表,按月分表
message db_call_dispatch_history {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //发起对讲机/用户所属的群组
  string org_id = 2;

  //@table timestamp
  //调度时间
  string action_time = 3;
  //@table uuid
  //调度人员rid,如果是中心软件发起,则此为发起用户的rid
  string person_id = 4;
  //@table varchar(16)
  //发起调度的对讲机dmrid,如果是中心软件发起的则为00000000
  string device_id = 6;

  //@table varchar(16)
  //接收调度命令的控制器DMR-ID,
  string controller_dmrid = 7;

  //@table text
  //调度命令中的目标dmrid列表,逗号分隔,级别调度时为一个,如果是信道切换则可能有多个
  string dispatch_target_dmrid = 8;

  //@table int
  //调度类型bc11: 01=开启紧急信道调度	02=开启紧急基站调度	00=取消紧急信道调度,  cb21: 111=切换信道 110=取消切换信道
  int32 dispatch_code = 10;

  //@table int
  //调度类型,参考bc11 C_TP
  int32 dispatch_type = 11;

  //@table int
  //目标信道
  int32 target_channel = 12;
}
//基站调度历史
message db_conf_dispatch_history {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //发起对讲机/用户所属的群组
  string org_id = 2;

  //@table timestamp
  //调度开始时间
  string start_time = 3;

  //@table uuid
  //调度人员rid,如果是中心后台发起的则为'00000000-0000-0000-0000-000000000000'
  string start_person_rid = 5;
  //@table text
  //被调度的控制器dmrid,逗号分隔
  string controller_ids = 6;

  //@table text
  //调度的会议室号
  string conference_no = 7;

  //@table uuid
  //结束会议对讲机/用户所属的群组
  string end_org_id = 8;

  //@table timestamp
  //调度结束时间
  string end_time = 9;
  //@table uuid
  //结束调度人员rid,如果是中心后台发起的则为'00000000-0000-0000-0000-000000000000'
  string end_person_rid = 10;

  //@table int
  //调度类型
  //1:基站互联，全部通话互通
  //0：仅仅信道互联，通话目标使用发话对讲机的目标id，类似信道补点
  int32 dispatch_code = 11;
}

//未确认短信表
message db_not_confirm_sms {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //发起对讲机/用户所属的群组
  string org_id = 2;

  //@table timestamp
  //短信发起时间
  string start_time = 3;

  //@table text
  //发起者dmrid
  string sender_dmrid = 4;

  //@table text
  //接收方dmrid
  string target_dmrid = 5;

  //@table text
  //接收中继
  string receive_repeater = 6;

  //@table text
  //短信内容
  string sms_content = 7;

  //@table text
  //短信序号
  string sms_no = 8;

  //@table text
  //发送设备的用户rid
  string sender_user_rid = 9;

  //@table text
  //一些其它记录,如多次发送记录
  string note = 10;

  //@table text default '02'
  //短信类型 02:普通短信 12:重要短信
  string sms_type = 11;
}

//短信历史表,短信一般很少,不分表处理了
message db_sms_history {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //发起对讲机/用户所属的群组
  string org_id = 2;

  //@table timestamp
  //短信发起时间
  string start_time = 3;

  //@table text
  //发起者dmrid
  string sender_dmrid = 4;

  //@table text
  //接收方dmrid
  string target_dmrid = 5;

  //@table text
  //接收中继
  string receive_repeater = 6;

  //@table text
  //短信内容
  string sms_content = 7;

  //@table text
  //短信序号
  string sms_no = 8;

  //@table timestamp
  //如果是单呼短信,这个是接收方确认时间
  string confirm_time = 9;

  //@table text
  //一些其它记录,如多次发送记录
  string note = 10;

  //@table text
  //发送设备的用户rid
  string sender_user_rid = 11;

  //@table text
  //短信类型 02:普通短信 12:重要短信
  string sms_type = 12;
}

//频道物理数据
//todo 此处信息还需要商议下才能确定
message db_ch_rf_setting {

  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //所属的群组
  string org_id = 2;

  //@table text  not null
  //名称
  string name = 3;

  //@table text
  //配置参数,一些公式的可以提取出来,或者json放settings里面
  string rf_setting = 4;

  //@table jsonb default '{}'::jsonb
  //辅助设置数据,Json格式
  string settings = 14;
}

//写频配置文件
message db_device_setting_conf {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //所属的群组
  string org_id = 2;

  //@table text  not null
  //名称
  string conf_name = 3;

  //@table timestamp
  //最后修改时间(保存时间)
  string last_modify_time = 4;

  //@table text
  //保存此配置的用户名
  string user_name = 5;

  //@table jsonb default '[]'::jsonb
  //写频
  string conf = 14;
}

//电话网关短号
message db_phone_short_no {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //所属的群组
  string org_id = 2;

  //@table text  unique not null
  //名称
  string short_no = 3;

  //@table timestamp
  //最后修改时间(保存时间)
  string last_modify_time = 4;

  //@table uuid REFERENCES db_org(rid) ON DELETE set null
  //对应的群组rid
  string ref_org_id = 5;

  //@table uuid REFERENCES db_device(rid) ON DELETE set null
  //对应的设备rid
  string ref_dev_id = 6;

  //@table text
  string note = 8;

  //@table jsonb default '{}'::jsonb
  //json设置
  string setting = 14;
}

//电话网关使用授权
message db_phone_gateway_permission {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //所属的群组
  string org_id = 2;

  //@table text
  //名称
  string name = 3;

  //@table timestamp
  //最后修改时间(保存时间)
  string last_modify_time = 4;

  //@table uuid REFERENCES db_org(rid) ON DELETE CASCADE
  //授权可以使用的群组rid
  string perm_org_id = 5;

  //@table uuid REFERENCES db_device(rid) ON DELETE CASCADE
  //授权可以使用的设备rid
  string perm_dev_id = 6;

  //@table uuid REFERENCES db_device(rid) ON DELETE CASCADE
  //网关rid
  string gateway_rid = 7;

  //@table text
  string note = 8;

  //@table jsonb default '{}'::jsonb
  // json设置
  string setting = 14;
}

//电话网关设备关系管理
message db_controller_gateway_manage {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //所属的群组
  string org_id = 2;

  //@table uuid REFERENCES db_controller(rid) ON DELETE CASCADE
  //电话网关控制器 rid
  string ref_controller_id = 3;

  //@table int
  //控制器板上电话接口位置,1,2,3
  int32 phone_pos = 4;

  //@table text
  //电话号码
  string phone_no = 5;

  //@table uuid unique REFERENCES db_device(rid) ON DELETE CASCADE
  //电话网关dev rid
  string ref_dev_id = 6;
}

//预定义电话号码本
message db_phone_no_list {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //所属的群组
  string org_id = 2;

  //@table timestamp
  //最后修改时间(保存时间)
  string last_modify_time = 4;

  //@table text  unique not null
  //名称
  string phone_name = 7;

  //@table text
  string phone_no = 8;

  //@table jsonb default '{}'::jsonb
  // json设置
  string setting = 14;
}

//有源点报警历史
message db_linepoint_alarm_history {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //对讲机所属的群组
  string org_id = 2;

  //@table timestamp
  //巡查时间
  string check_time = 3;
  //@table uuid
  //巡查人员rid
  string checker_id = 4;
  //@table uuid
  //对讲机rid
  string device_id = 6;

  //@table timestamp
  //接收时间,不一定是实时的
  string receive_time = 8;

  //@table varchar(16)
  //接收的控制器名称
  string receiver = 9;

  //@table uuid REFERENCES db_line_point(rid) ON DELETE CASCADE
  //点的标识
  string point_id = 10;

  //@table int
  //报警码,低压报警为4
  int32 alarm_code = 11;
}

message db_device_channel_zone {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table int default 0
  //区域所在层级,0,1,2,3,0为最顶级(root),用户看不见
  int32 zone_level = 2;

  //@table int
  //区域在所在层级下的序号,一个父区域下可以有多个子区域,这些子区域必须按顺序编号,从0开始
  int32 zone_no = 3;

  //@table text
  //区域名称
  string zone_title = 4;

  //@table uuid REFERENCES db_device_channel_zone(rid) ON DELETE CASCADE
  //区域的上级,最顶级时为null
  string zone_parent = 5;

  //@table jsonb not null default '{}'::jsonb
  //setting
  string setting = 6;
}

//用户crud log表
message db_crud_log {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid
  //用户所属的群组
  string org_rid = 2;

  //@table uuid
  //用户rid
  string user_rid = 3;

  //@table text
  //操作
  string operation = 4;

  //@table jsonb
  //req proto json
  string req = 5;

  //@table text
  //req option
  string req_option = 6;

  //@table text
  //ipinfo
  string ip_info = 7;

  //@table jsonb
  //note
  string note = 8;

  //@table timestamp not null default now_utc()
  //数据最后修改时间
  string update_at = 14;
}

//动态组成员详细信息
message db_dynamic_group_detail {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid  REFERENCES db_org(rid) ON DELETE CASCADE
  //组成员所属的动态组
  string org_id = 2;

  //@table uuid  REFERENCES db_device(rid) ON DELETE CASCADE
  //终端rid
  string device_rid = 3;

  //@table varchar(8)
  //终端DMR ID
  string device_dmrid = 4;

  //@table uuid  REFERENCES db_org(rid) ON DELETE CASCADE
  //组呼rid
  string group_rid = 5;

  //@table varchar(8)
  //组呼DMR ID
  string group_dmrid = 6;

  //@table int
  //此成员是终端还是组呼 1:终端 2:组呼 3:因任务组呼加入的终端
  int32 is_device_group = 7;

  //@table int default 0
  //在动态组中的状态
  //临时组：1：正常 2：被优先级高的抢占 10:已失效 11:已删除(用于回应客户端)
  //任务组：1:正常/已应答加入，2：被优先级高的抢占， 4:未应答加入 5:已应答退出 6:未应答退出
  int32 member_state = 8;

  //@table int default 0
  //动态组类型 0:临时组 1：任务组
  int32 dynamic_group_type = 9;

  //@table uuid
  //组成员所属的单位rid
  string member_org_id = 10;

  //@table int default 0
  //动态组状态
  //临时组：1：正常 10：失效
  //任务组：1：正常 10：删除中
  int32 dynamic_group_state = 11;

  //@table timestamp
  //任务组中终端的确认时间
  string task_confirm_time = 12;

  //@table text
  //创建者的rid
  string creator = 13;

  //@table timestamp
  string create_time = 15;
}

//物联网终端
message db_iot_device {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid  REFERENCES db_org(rid) ON DELETE CASCADE
  //所属的群组
  string org_id = 2;

  //@table text not null unique
  //终端id
  string dev_id = 3;

  //@table int
  //终端类型 1:对讲机 2:工牌 3:物卡 4：烟感  5：节能灯  6：温湿度
  sint32 dev_type = 4;

  //@table text
  //名称
  string dev_name = 6;

  //@table text
  //详细描述
  string note = 7;

  //@table double precision
  //经度
  double lon = 8;

  //@table double precision
  //纬度
  double lat = 9;

  //@table jsonb not null default '{}'::jsonb
  //配置信息
  string setting = 10;

  //@table text
  //创建者的rid
  string creator = 13;

  //@table timestamp
  string create_time = 15;
}

//iot限制
message db_iot_restriction {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid  REFERENCES db_org(rid) ON DELETE CASCADE
  //iot所属的群组
  string org_id = 2;

  //@table text not null unique
  //iot id
  string iot_id = 3;

  //@table int not null default 0
  //限制类型 0:只允许进入基站 1：禁止进入基站 2:定时上报
  int32 restrict_type = 4;

  //@table uuid  REFERENCES db_line_point(rid) ON DELETE CASCADE
  //限制的基站 restrict_type=0/1时用
  string restrict_station_rid = 5;

  //@table int not null default 0
  //定时上报间隔(秒)
  sint32 restrict_alive_interval = 6;

  //@table text
  //创建者的rid
  string creator = 13;

  //@table timestamp
  string create_time = 15;
}

//物联终端最后的数据信息
message db_iot_device_last_info {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table timestamp not null default now_utc()
  //最后修改时间
  string update_at = 2;

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE default '00000000-0000-0000-0000-000000000000'
  //设备所属的群组
  string org_id = 3;

  //@table varchar(16) not null unique
  //iot设备ID
  string dev_id = 5;

  //@table timestamp  default '2000-01-01 00:00:00'
  //最后数据时间
  string last_data_time = 6;

  //@table int not null default 0
  //最后命令值
  sint32 last_cmd = 7;

  //@table text default ''
  //最后的状态信息
  string dev_status = 14;

  //@table text default ''
  //最后接收的控制器id
  string last_controller = 15;

  //@table jsonb not null default '{}'::jsonb
  //额外的状态信息
  string opt_status = 19;
}

//iot_data历史表,按月分表
message db_iot_data_history {
  //@table uuid primary key
  //行ID
  string rid = 1;
  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  //iot dev所属的群组
  string org_id = 2;

  //@table timestamp
  //接收时间
  string cmd_time = 3;

  //@table int not null default 0
  //命令字
  sint32 cmd = 4;

  //@table int not null default 0
  //设备类型  3:物卡 4:烟感  5:节能灯  6:温湿度
  int32 dev_type = 5;
  //@table text
  //iot dev id
  string dev_id = 6;

  //@table text
  //接收基站ID
  string recv_station_id = 7;

  //@table timestamp
  //后台接收时间
  string recv_time = 8;

  //@table varchar(16)
  //接收的控制器id
  string receiver = 9;

  //@table text not null default ''
  //接收的其它参数
  string cmd_param = 10;
}

//设备固定订阅/静态收听表
message db_static_subscribes {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table text not null references db_controller(dmr_id) on update cascade ON DELETE CASCADE
  //控制器dmrid
  string controller_dmr_id = 2;

  //@table varchar(8) not null REFERENCES db_org(dmr_id) ON update CASCADE ON DELETE CASCADE
  //群组dmrid
  string group_dmr_id = 3;

  //@table text
  //创建者的rid
  string creator = 13;

  //@table timestamp
  string create_time = 15;
}


//app用户地图显示中特别许可的其它终端列表
message db_app_map_privilege_device {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table varchar(16) not null REFERENCES db_device(dmr_id) ON DELETE CASCADE
  //app终端dmrid
  string app_dmrid = 2;


  //@table varchar(16) not null REFERENCES db_device(dmr_id) ON DELETE CASCADE
  //许可的终端dmrid
  string grant_device_dmrid = 3;

  //@table text
  //许可人rid
  string grant_user_rid = 4;

  //@table timestamp default '2000-01-01 00:00:00'
  //许可到期时间
  string expire_time = 5;

  //@table int default 0
  //是否设置了许可超时时间
  int32 is_set_expire = 6;

  //@table timestamp
  //申请许可时间
  string apply_time = 7;

  //@table timestamp
  //授予许可时间
  string grant_time = 8;

  //@table text
  //许可人名称
  string grant_user_name = 9;
}

// poc session id表
message db_poc_session {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table text not null references db_device(dmr_id) on update cascade ON DELETE CASCADE
  //poc终端dmrid
  string poc_dmrid = 2;

  //@table uuid not null
  //session id
  string session_id = 3;

  //@table timestamp
  //login时间
  string login_time = 4;

  //@table text
  //ip info
  string ip_info = 5;

  //@table timestamp
  //last update time
  string last_update_time = 6;
}

// 常用组呼联系人列表
message db_group_call_contacts {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_user(rid) ON DELETE CASCADE
  //用户rid
  string user_rid = 2;

  //@table text not null REFERENCES db_org(dmr_id) ON DELETE CASCADE
  //组呼dmrid
  string group_dmrid = 3;

  //@table int default 0
  //排序值
  int32 sort_value = 4;

  //@table uuid
  //创建人rid
  string creator_rid = 5;

  //@table timestamp
  //创建时间
  string create_time = 6;

}

// 常用单呼联系人列表
message db_single_call_contacts {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table uuid not null REFERENCES db_user(rid) ON DELETE CASCADE
  //用户rid
  string user_rid = 2;

  //@table text not null REFERENCES db_device(dmr_id) ON DELETE CASCADE
  //单呼dmrid
  string single_dmrid = 3;

  //@table int default 0
  //排序值
  int32 sort_value = 4;

  //@table uuid
  //创建人rid
  string creator_rid = 5;

  //@table timestamp
  //创建时间
  string create_time = 6;
}