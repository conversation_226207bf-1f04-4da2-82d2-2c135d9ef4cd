<template>
  <bf-dialog
    v-model="visible"
    ref="audioSwitch"
    :title="$t('dialog.selectListenGroup')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :modal="false"
    class="header-border shadow-md shadow-slate-800 audio-switch-dialog drag-dialog"
    modal-class="drag-dialog-modal"
    append-to-body
    draggable
    center
  >
    <send-group-tree class="!w-[320px] h-full m-auto" :is-in-page-header="false"></send-group-tree>
  </bf-dialog>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue'
  import bfDialog from '@/components/bfDialog/main'

  // 接收从openDialog传递的dialogVisible属性
  const props = defineProps<{
    dialogVisible?: boolean
  }>()

  // 定义emit事件，用于更新dialogVisible
  const emit = defineEmits<{
    'update:dialogVisible': [value: boolean]
  }>()

  // 内部状态
  const visible = ref(false)

  // 监听props.dialogVisible的变化
  watch(
    () => props.dialogVisible,
    newVal => {
      if (newVal !== undefined) {
        visible.value = newVal
      }
    },
    { immediate: true }
  )

  // 监听内部visible的变化，同步到父组件
  watch(visible, newVal => {
    emit('update:dialogVisible', newVal)
  })
</script>

<style lang="scss">
  .audio-switch-dialog.el-dialog {
    --el-dialog-width: 350px;
    height: 60vh;
  }
</style>
