import { formatDmrIdLabel, getDynamicGroupOrgType, DefOrgRid } from '@/utils/bfutil'

const dynamicGroupOrgType = getDynamicGroupOrgType()
const dynamicGroupStatusClasses = {
  // 0: 'init',
  1: 'valid',
  10: 'invalid', // 3: 'deldete',
}

/**
 * @param orgItem DbOrg
 */
export function getOrgTaskGroupStatus<T extends { orgIsVirtual?: number; dynamicGroupState?: number }>(orgItem: T | null): string {
  if (!orgItem) {
    return ''
  }

  // 不是动态组，没有状态
  if (!dynamicGroupOrgType.includes(orgItem.orgIsVirtual)) {
    return ''
  }

  // dynamic_group_state  1:正常 10:失效 / 删除中
  return dynamicGroupStatusClasses[orgItem.dynamicGroupState] || dynamicGroupStatusClasses[10]
}

const orgNodeIconFonts = {
  1: 'bfdx-xunizuzhiguanli',
  100: 'bfdx-a-zuzhiguanli3',
  102: 'bfdx-a-zuzhiguanli3',
  101: 'bfdx-a-zuzhiguanli2',
}

/**
 * @param orgItem DbOrg
 */
export function getOrgNodeIcon<T extends { orgIsVirtual?: number }>(orgItem: T | null): string | false {
  if (!orgItem) {
    return false
  }
  // 1：虚拟组，2：真实组，101：任务组，100：临时组
  return orgNodeIconFonts[orgItem.orgIsVirtual] || false
}

// 获取组织节点的 Tooltip
export function getOrgNodeTooltip<T extends { dmrId: string }>(orgItem: T): string {
  const hex = orgItem.dmrId
  const masked = (Number(`0x${hex}`) & 0x7ffff) >>> 0
  return `${formatDmrIdLabel(hex)} (${masked})`
}

export function getDeviceNodeTooltip<T extends { dmrId: string; deviceUser: string; lastRfidPerson: string }>(device: T): string {
  const hex = device.dmrId
  const masked = (Number(`0x${hex}`) & 0x7ffff) >>> 0
  const dmrIdLabel = `${formatDmrIdLabel(device.dmrId)} (${masked})`

  let userRid = device.deviceUser
  // 如果有终端读卡，则使用最后读卡的人员
  if (device.lastRfidPerson && device.lastRfidPerson !== DefOrgRid) {
    userRid = device.lastRfidPerson
  }
  const userData = bfglob.guserData.get(userRid)
  const userPhone = userData?.userPhone ?? ''
  if (userPhone) {
    return `${dmrIdLabel} ${userPhone}`
  }

  return dmrIdLabel
}

/** org终端数量相关函数 ------ 开始 */

export function clearTreeOrgDeviceCount() {
  const g_org = bfglob.gorgData.getAll()
  for (const i in g_org) {
    g_org[i].ownDeviceCount = 0
    g_org[i].sumDeviceCount = -1
  }
}

/**
 * sumDeviceCount:org本身的终端数量以及子org的终端数量总和
 * ownDeviceCount:org本身的终端数量
 * @param org_item dbOrg
 * @returns number sumDeviceCount
 */
export function calculateTreeOrgSubOrgDeviceCount(org_item) {
  if (org_item.orgIsVirtual === 1) {
    return org_item.ownDeviceCount
  }
  const sub_org = []
  const g_org = bfglob.gorgData.getAll()
  for (const i in g_org) {
    if (g_org[i].parentOrgId === org_item.rid) {
      if (org_item.rid !== g_org[i].rid) {
        sub_org.push(g_org[i])
      }
    }
  }

  let result = org_item.ownDeviceCount
  for (const j in sub_org) {
    if (sub_org[j].orgIsVirtual === 1) {
      continue
    }

    if (sub_org[j].sumDeviceCount >= 0) {
      result += sub_org[j].sumDeviceCount
    } else {
      result += calculateTreeOrgSubOrgDeviceCount(sub_org[j])
    }
  }

  return result
}

export function updateTreeOrgDeviceCount() {
  clearTreeOrgDeviceCount()
  const g_org = bfglob.gorgData.getAll()
  const g_device = bfglob.gdevices.getAll()
  for (const i in g_org) {
    for (const j in g_device) {
      if (g_device[j].orgId === g_org[i].rid) {
        g_org[i].ownDeviceCount++
      }
      if (g_device[j].virOrgs.includes(g_org[i].rid)) {
        g_org[i].ownDeviceCount++
      }
    }
  }

  // sum all the device of the org
  for (const k in g_org) {
    if (g_org[k].sumDeviceCount >= 0) {
      continue
    }
    g_org[k].sumDeviceCount = calculateTreeOrgSubOrgDeviceCount(g_org[k])
  }
}

/** org终端数量相关函数 ------ 结束 */
