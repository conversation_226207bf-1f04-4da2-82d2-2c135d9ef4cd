<script setup lang="ts">
  import { TreeNodeData } from 'element-plus'
  import { TreeNodeType } from './types'
  import { computed, useTemplateRef } from 'vue'
  import { getDeviceNodeTooltip, getOrgNodeTooltip } from './treeUtil'
  import { get_device_status_className as getDeviceStatusClassName } from '@/utils/bftree'

  const { enableCheckbox, row, checked, indeterminate, toggleCheckboxEvent } = defineProps<{
    enableCheckbox?: boolean
    row: TreeNodeData
    checked?: boolean
    indeterminate?: boolean
    toggleCheckboxEvent: (row: TreeNodeData) => void
  }>()

  const getNodeOriginData = (row: TreeNodeData) => {
    if (row.nodeType === TreeNodeType.Org) {
      return bfglob.gorgData.get(row.rid)
    } else {
      return bfglob.gdevices.get(row.rid)
    }
  }
  const contextRef = useTemplateRef('contextRef')
  const isOverflow = computed(() => {
    const el = contextRef.value
    if (!el) return false
    return el.scrollWidth > el.clientWidth
  })

  const tooltipContent = computed(() => {
    const originData = getNodeOriginData(row)
    if (row.nodeType === TreeNodeType.Org) {
      return getOrgNodeTooltip(originData)
    } else {
      return getDeviceNodeTooltip(originData)
    }
  })
</script>

<template>
  <div class="vxe-column-content-wrapper relative w-full inline-flex gap-[5px]">
    <span v-if="enableCheckbox" class="vxe-column-checkbox" @click.stop="toggleCheckboxEvent(row)">
      <i v-if="indeterminate" class="bf-iconfont bfdx-fuxuanduoanniu"></i>
      <i v-else-if="checked" class="bf-iconfont bfdx-shuzhuang"></i>
      <i v-else class="bf-iconfont bfdx-dingweimaodianwaikuang"></i>
    </span>
    <el-tooltip popper-class="bf-tooltip" effect="dark" placement="bottom" :show-after="1000" :content="isOverflow ? tooltipContent + '...' : tooltipContent">
      <div v-if="row.nodeType === TreeNodeType.Org" ref="contextRef" class="vxe-column-content inline-flex justify-start items-center gap-[10px]">
        {{ getNodeOriginData(row)?.orgShortName }}
        <div class="h-[15px] leading-[15px]! px-[5px] bg-[#1a7aff] rounded-[4px] text-[11px]" v-if="getNodeOriginData(row)?.sumDeviceCount > 0">
          {{ getNodeOriginData(row)?.sumDeviceCount }}
        </div>
      </div>
      <div class="vxe-column-content inline-flex" ref="contextRef" v-else>
        <i
          v-show="getDeviceStatusClassName(getNodeOriginData(row)) !== 'device_status_none'"
          :class="['bf-iconfont bfdx-yansedian mr-[5px]', getDeviceStatusClassName(getNodeOriginData(row))]"
        ></i>
        {{ getNodeOriginData(row)?.selfId }}
      </div>
    </el-tooltip>
  </div>
</template>

<style lang="scss" scoped>
  .vxe-column-checkbox {
    * {
      color: #1a7aff;
      cursor: pointer;
    }
  }

  .vxe-column-content {
    padding: 0 5px;
    cursor: default;
  }

  $device_status_color_light_gray: rgb(123, 144, 161, 0.7);
  $device_status_color_gray: #949494;
  $device_status_color_yellow: #ffff1e;
  $device_status_color_green: #2fffa2;
  $device_status_color_red: #ff5833;

  @mixin device_status_base {
    width: 20px;
    height: 20px;
    font-size: 12px;
    position: relative;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    border-width: 1px;
    border-style: solid;
    border-radius: 50%;
    border-color: $device_status_color_gray;
    color: $device_status_color_gray;
  }

  .device_status {
    &_none {
      @include device_status_base;
      display: none;
    }

    &_yellow {
      @include device_status_base;
      border-color: $device_status_color_yellow;
      color: $device_status_color_yellow;
    }

    &_light_gray {
      @include device_status_base;
      border-color: $device_status_color_light_gray;
      color: $device_status_color_light_gray;
    }

    &_gray {
      @include device_status_base;
    }

    &_green {
      @include device_status_base;
      border-color: $device_status_color_green;
      color: $device_status_color_green;
    }

    &_red {
      @include device_status_base;
      border-color: $device_status_color_red;
      color: $device_status_color_red;
    }

    &_other_red {
      @include device_status_base;

      &:after {
        content: '';
        display: block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        position: absolute;
        top: 4px;
        right: 4px;
        z-index: 10;
        background-color: $device_status_color_red;
      }
    }

    &_icon_th {
      @include device_status_base;
      // todo add speaking icon
    }

    &_emergency_green {
      @include device_status_base;
      border-color: $device_status_color_green;
      color: $device_status_color_green;

      animation: _emergency_green 0.5s infinite;
    }
    @keyframes _emergency_green {
      0% {
        border-color: $device_status_color_green;
        color: $device_status_color_green;
      }
      50% {
        border-color: $device_status_color_red;
        color: $device_status_color_red;
      }
      100% {
        border-color: $device_status_color_green;
        color: $device_status_color_green;
      }
    }

    &_emergency_yellow {
      @include device_status_base;

      border-color: $device_status_color_yellow;
      color: $device_status_color_yellow;
      animation: _emergency_yellow 0.5s infinite;
    }
    @keyframes _emergency_yellow {
      0% {
        border-color: $device_status_color_yellow;
        color: $device_status_color_yellow;
      }
      50% {
        border-color: $device_status_color_red;
        color: $device_status_color_red;
      }
      100% {
        border-color: $device_status_color_yellow;
        color: $device_status_color_yellow;
      }
    }
  }
</style>
