/** 通话联系人管理相关操作，GroupCallContacts and SingleCallContacts */

import dbCmd from '@/modules/protocol/db.pb.cmd'
import bfproto from '@/modules/protocol'
import { getDbSubject } from '@/utils/bfutil'

export function GetDbGroupCallContactsFromServer(userRid: string) {
  const msgObj = {
    userRid: userRid,
  }
  const msgOpts = {
    rpcCmdFields: {
      origReqId: 'user_rid',
      resInfo: '*',
    },
    decodeMsgType: 'db_group_call_contacts_list',
  }

  bfproto
    .sendMessage(dbCmd.Db, msgObj, 'db_group_call_contacts', getDbSubject(), msgOpts)
    .then(rpc_cmd_obj => {
      const db_group_call_contacts_list_obj = rpc_cmd_obj.body
      if (typeof db_group_call_contacts_list_obj.rows === 'undefined' || db_group_call_contacts_list_obj.rows.length === 0) {
        return
      }
      const rows = db_group_call_contacts_list_obj.rows
      for (const i in rows) {
        const item = rows[i]
        bfglob.gGroupCallContacts.set(item.rid, item, item.groupDmrid)
      }
    })
    .catch(err => {
      bfglob.console.warn('获取用户单位通话联系人失败', err)
    })
}

export function GetDbSingleCallContactsFromServer(userRid: string) {
  const msgObj = {
    userRid: userRid,
  }
  const msgOpts = {
    rpcCmdFields: {
      origReqId: 'user_rid',
      resInfo: '*',
    },
    decodeMsgType: 'db_single_call_contacts_list',
  }

  bfproto
    .sendMessage(dbCmd.Db, msgObj, 'db_single_call_contacts', getDbSubject(), msgOpts)
    .then(rpc_cmd_obj => {
      const db_single_call_contacts_list_obj = rpc_cmd_obj.body
      if (typeof db_single_call_contacts_list_obj.rows === 'undefined' || db_single_call_contacts_list_obj.rows.length === 0) {
        return
      }
      const rows = db_single_call_contacts_list_obj.rows
      for (const i in rows) {
        const item = rows[i]
        bfglob.gSingleCallContacts.set(item.rid, item, item.groupDmrid)
      }
    })
    .catch(err => {
      bfglob.console.warn('获取用户终端通话联系人失败', err)
    })
}

export function AddDbGroupCallContact(data) {
  bfglob.gGroupCallContacts.set(data.rid, data, data.groupDmrid)
  bfglob.emit('add_global_group_call_contacts', data)
}

export function UpdateDbGroupCallContact(data) {
  const oldData = bfglob.gGroupCallContacts.get(data.rid)
  // 删除旧的索引
  bfglob.gGroupCallContacts.deleteIndexByKey(oldData.groupDmrid)
  const t = bfproto.bfdx_proto_msg_T('db_group_call_contacts')
  const d = t.create(data)
  const newData = Object.assign(d, oldData, data)
  bfglob.gGroupCallContacts.set(data.rid, data, data.groupDmrid)
  bfglob.emit('update_global_group_call_contacts', newData)
}

export function DeleteDbGroupCallContact(data) {
  bfglob.gGroupCallContacts.delete(data.rid)
  bfglob.emit('delete_global_group_call_contacts', data)
}

export function AddDbSingleCallContact(data) {
  bfglob.gSingleCallContacts.set(data.rid, data, data.singleDmrid)
  bfglob.emit('add_global_single_call_contacts', data)
}

export function UpdateDbSingleCallContact(data) {
  const oldData = bfglob.gSingleCallContacts.get(data.rid)
  if (!oldData) {
    return
  }
  const t = bfproto.bfdx_proto_msg_T('db_single_call_contacts')
  const d = t.create(data)
  const newData = Object.assign(d, oldData, data)
  bfglob.gSingleCallContacts.set(data.rid, data, data.singleDmrid)
  bfglob.emit('update_global_single_call_contacts', newData)
}

export function DeleteDbSingleCallContact(data) {
  bfglob.gSingleCallContacts.delete(data.rid)
  bfglob.emit('delete_global_single_call_contacts', data)
}
